<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" />
    <meta name="renderer" content="webkit" />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no"
    />
    <link rel="icon" href="<%= BASE_URL %>favicon.ico" />
    <title><%= webpackConfig.name %></title>
    <!--[if lt IE 11
      ]><script>
        window.location.href = '/html/ie.html'
      </script><!
    [endif]-->
    <style>
      html,
      body,
      #app {
        height: 100%;
        margin: 0px;
        padding: 0px;
      }

      :root {
        /* 默认使用深色主题变量，国网绿 */
        --theme-bg: #00907f;
        --theme-color: #fff;
        --theme-primary: #00907f;
      }

      /* 浅色主题变量，国网蓝 */
      :root[data-theme='theme-light'] {
        --theme-bg: #3490fb;
        --theme-color: #fff;
        --theme-primary: #3490fb;
      }

      /* 深色主题变量，国网绿 */
      :root[data-theme='theme-dark'] {
        --theme-bg: #00907f;
        --theme-color: #fff;
        --theme-primary: #00907f;
      }

      #loader-wrapper {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        z-index: 999999;
        background: var(--theme-bg);
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        transition: background-color 0.3s ease;
      }

      .loading-dots {
        display: flex;
        justify-content: center;
        align-items: center;
        gap: 12px;
      }

      .dot {
        width: 16px;
        height: 16px;
        background: var(--theme-primary); /* 使用主题色变量 */
        border-radius: 50%;
        animation: bounce 0.5s ease-in-out infinite;
      }

      .dot:nth-child(2) {
        animation-delay: 0.1s;
      }

      .dot:nth-child(3) {
        animation-delay: 0.2s;
      }

      @keyframes bounce {
        0%,
        100% {
          transform: translateY(0);
        }
        50% {
          transform: translateY(-20px);
        }
      }

      .load_title {
        color: var(--theme-color);
        font-size: 18px;
        margin-top: 30px;
        font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB',
          'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
      }

      .loaded #loader-wrapper {
        visibility: hidden;
        opacity: 0;
        transition: all 0.3s ease-out;
      }
    </style>
  </head>
  <body>
    <div id="app">
      <div id="loader-wrapper">
        <div class="loading-dots">
          <div class="dot"></div>
          <div class="dot"></div>
          <div class="dot"></div>
        </div>
        <div class="load_title">正在加载系统资源，请耐心等待</div>
      </div>
    </div>
    <script>
      // 读取本地存储的主题设置
      const theme = localStorage.getItem('theme') || 'theme-dark'
      // 设置根元素的data-theme属性
      document.documentElement.setAttribute('data-theme', theme)

      // 根据主题设置主题色
      const primaryColor = theme === 'theme-dark' ? '#00907F' : '#3490FB'
      document.documentElement.style.setProperty('--theme-primary', primaryColor)

      // 额外设置Element UI主题变量，确保刷新页面后立即应用
      document.documentElement.style.setProperty('--el-color-primary', primaryColor)

      // 设置其他相关主题颜色
      const themeColors = {
        'theme-dark': {
          '--el-color-primary-dark-2': '#007a6b',
          '--el-color-primary-light-3': '#33a696',
          '--el-color-primary-light-5': '#66bfb2',
          '--el-color-primary-light-7': '#99d9cf',
          '--el-color-primary-light-8': '#b3e5dd',
          '--el-color-primary-light-9': '#ccf2ec'
        },
        'theme-light': {
          '--el-color-primary-dark-2': '#2c7ad4',
          '--el-color-primary-light-3': '#5ca6fc',
          '--el-color-primary-light-5': '#85bdfd',
          '--el-color-primary-light-7': '#add4fe',
          '--el-color-primary-light-8': '#c2e0fe',
          '--el-color-primary-light-9': '#d6ebfe'
        }
      }

      // 应用当前主题的颜色变量
      const currentThemeColors = themeColors[theme]
      for (const [key, value] of Object.entries(currentThemeColors)) {
        document.documentElement.style.setProperty(key, value)
      }
    </script>
  </body>
</html>
