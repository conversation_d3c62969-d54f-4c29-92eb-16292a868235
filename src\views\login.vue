<template>
  <div class="login" :class="themeClass">
    <el-form ref="loginForm" :model="loginForm" :rules="loginRules" class="login-form">
      <h3 class="title">码上通管理系统</h3>
      <el-form-item prop="username">
        <el-input v-model="loginForm.username" type="text" auto-complete="off" placeholder="账号">
          <svg-icon slot="prefix" icon-class="user" class="el-input__icon input-icon" />
        </el-input>
      </el-form-item>
      <el-form-item prop="password">
        <el-input
          v-model="loginForm.password"
          type="password"
          auto-complete="off"
          placeholder="密码"
          @keyup.enter.native="handleLogin"
        >
          <svg-icon slot="prefix" icon-class="password" class="el-input__icon input-icon" />
        </el-input>
      </el-form-item>
      <el-form-item prop="code" v-if="captchaEnabled">
        <el-input
          v-model="loginForm.code"
          auto-complete="off"
          placeholder="验证码"
          style="width: 63%"
          @keyup.enter.native="handleLogin"
        >
          <svg-icon slot="prefix" icon-class="validCode" class="el-input__icon input-icon" />
        </el-input>
        <div class="login-code">
          <img :src="codeUrl" @click="getCode" class="login-code-img" />
        </div>
      </el-form-item>
      <el-checkbox v-model="loginForm.rememberMe" style="margin: 0px 0px 25px 0px"
        >记住密码</el-checkbox
      >
      <el-form-item style="width: 100%">
        <el-button
          :loading="loading"
          size="medium"
          type="primary"
          style="width: 100%"
          @click.native.prevent="handleLogin"
        >
          <span v-if="!loading">登 录</span>
          <span v-else class="custom-loading-text">登 录 中...</span>
        </el-button>
        <div style="float: right" v-if="register">
          <router-link class="link-type" :to="'/register'">立即注册</router-link>
        </div>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
  import { getCodeImg } from '@/api/login'
  import Cookies from 'js-cookie'
  import { encrypt, decrypt } from '@/utils/jsencrypt'

  export default {
    name: 'Login',
    data() {
      return {
        codeUrl: '',
        loginForm: {
          username: '',
          password: '',
          rememberMe: false,
          code: '',
          uuid: ''
        },
        loginRules: {
          username: [{ required: true, trigger: 'blur', message: '请输入您的账号' }],
          password: [{ required: true, trigger: 'blur', message: '请输入您的密码' }],
          code: [{ required: true, trigger: 'change', message: '请输入验证码' }]
        },
        loading: false,
        // 验证码开关
        captchaEnabled: false,
        // 注册开关
        register: false,
        redirect: undefined,
        themeClass: '' // 添加主题class
      }
    },
    watch: {
      $route: {
        handler: function (route) {
          this.redirect = route.query && route.query.redirect
        },
        immediate: true
      }
    },
    created() {
      this.getCookie()
      // 获取当前主题
      const theme = window.localStorage.getItem('theme') || 'theme-dark'
      this.themeClass = theme
    },
    methods: {
      getCode() {
        getCodeImg().then((res) => {
          this.captchaEnabled = res.captchaEnabled == null ? true : res.captchaEnabled
          if (this.captchaEnabled) {
            this.codeUrl = 'data:image/gif;base64,' + res.img
            this.loginForm.uuid = res.uuid
          }
        })
      },
      getCookie() {
        const username = Cookies.get('username')
        const password = Cookies.get('password')
        const rememberMe = Cookies.get('rememberMe')
        this.loginForm = {
          username: username === undefined ? this.loginForm.username : username,
          password: password === undefined ? this.loginForm.password : decrypt(password),
          rememberMe: rememberMe === undefined ? false : rememberMe === 'true'
        }
      },
      handleLogin() {
        this.$refs.loginForm.validate((valid) => {
          if (valid) {
            this.loading = true
            if (this.loginForm.rememberMe) {
              Cookies.set('username', this.loginForm.username, { expires: 30 })
              Cookies.set('password', encrypt(this.loginForm.password), { expires: 30 })
              Cookies.set('rememberMe', this.loginForm.rememberMe, { expires: 30 })
            } else {
              Cookies.remove('username')
              Cookies.remove('password')
              Cookies.remove('rememberMe')
            }
            this.$store
              .dispatch('Login', this.loginForm)
              .then(() => {
                this.$router.push({ path: this.redirect || '/performance' }) // 修改为新的成效看板路径
              })
              .catch(() => {
                this.loading = false
                if (this.captchaEnabled) {
                  this.getCode()
                }
              })
          }
        })
      }
    }
  }
</script>

<style rel="stylesheet/scss" lang="scss">
  .login {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100%;
    transition: background 0.3s ease;

    // 深色主题
    &.theme-dark {
      background: #013831;
      .title {
        color: #ffffff;
      }
      .login-form {
        background: rgba(255, 255, 255, 0.1);
        .el-input input {
          background: rgba(255, 255, 255, 0.1);
          border-color: rgba(255, 255, 255, 0.2);
          color: #ffffff;
          &::placeholder {
            color: rgba(255, 255, 255, 0.5);
          }
        }
        .el-checkbox__label {
          color: rgba(255, 255, 255, 0.8);
        }
        .link-type {
          color: rgba(255, 255, 255, 0.8);
        }
        .el-button--primary {
          background-color: #0e8e97;
          border-color: #0e8e97;
          &:hover,
          &:focus {
            background-color: #1a9ba4;
            border-color: #1a9ba4;
          }
        }
      }
    }

    // 浅色主题
    &.theme-light {
      background: #f0f2f5;
      .title {
        color: #1c663e;
      }
      .login-form {
        background: #ffffff;
        .el-button--primary {
          background-color: #1c663e;
          border-color: #1c663e;
          &:hover,
          &:focus {
            background-color: #2a744c;
            border-color: #2a744c;
          }
        }
      }
    }
  }

  .custom-loading-text {
    font-size: 14px;
    color: #ffffff;
  }

  .title {
    margin: 0px auto 30px auto;
    text-align: center;
    color: #1c663e;
    font-size: 24px;
    font-weight: bold;
  }

  .login-form {
    border-radius: 8px;
    background: #ffffff;
    width: 400px;
    padding: 35px 35px 15px 35px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);

    .el-input {
      height: 40px;

      input {
        height: 40px;
      }
    }

    .input-icon {
      height: 40px;
      width: 14px;
      margin-left: 2px;
    }

    .el-button--primary {
      background-color: #1c663e;
      border-color: #1c663e;

      &:hover,
      &:focus {
        background-color: #2a744c;
        border-color: #2a744c;
      }
    }
  }

  .login-tip {
    font-size: 13px;
    text-align: center;
    color: #bfbfbf;
  }

  .login-code {
    width: 33%;
    height: 38px;
    float: right;

    img {
      cursor: pointer;
      vertical-align: middle;
    }
  }

  .el-login-footer {
    height: 40px;
    line-height: 40px;
    position: fixed;
    bottom: 0;
    width: 100%;
    text-align: center;
    color: #fff;
    font-family: Arial;
    font-size: 12px;
    letter-spacing: 1px;
  }

  .login-code-img {
    height: 38px;
  }
</style>
