<template>
  <div class="login" :class="themeClass">
    <el-form ref="loginForm" :model="loginForm" :rules="loginRules" class="login-form">
      <h3 class="title">码上通管理系统</h3>

      <!-- Token登录模式 - 只显示登录状态 -->
      <div v-if="isTokenLogin" class="token-login-container">
        <div class="login-status">
          <!-- 登录中状态 -->
          <div v-if="!tokenLoginFailed">
            <i class="el-icon-loading" style="font-size: 24px; color: #409EFF;"></i>
            <p style="margin-top: 16px; font-size: 16px; color: #ffffff;">正在登录...</p>
          </div>
          <!-- 登录失败状态 -->
          <div v-else>
            <i class="el-icon-warning" style="font-size: 24px; color: #F56C6C;"></i>
            <p style="margin-top: 16px; font-size: 16px; color: #F56C6C;">Token登录失败</p>
            <el-button
              type="primary"
              size="medium"
              style="margin-top: 16px;"
              @click="retryTokenLogin"
              :loading="loading"
            >
              <span v-if="!loading">重新登录</span>
              <span v-else>登录中...</span>
            </el-button>
          </div>
        </div>
      </div>

      <!-- 没有token时显示错误提示 -->
      <div v-else class="token-login-container">
        <div class="login-status">
          <i class="el-icon-warning" style="font-size: 24px; color: #F56C6C;"></i>
          <p style="margin-top: 16px; font-size: 16px; color: #F56C6C;">没有获取到登录信息</p>
          <p style="margin-top: 8px; font-size: 14px; color: #909399;">请通过主系统进行登录访问</p>
        </div>
      </div>
    </el-form>
  </div>
</template>

<script>
  import { getCodeImg } from '@/api/login'
  import Cookies from 'js-cookie'
  import { encrypt, decrypt } from '@/utils/jsencrypt'

  export default {
    name: 'Login',
    data() {
      return {
        codeUrl: '',
        loginForm: {
          username: '',
          password: '',
          rememberMe: false,
          code: '',
          uuid: ''
        },
        loginRules: {
          username: [{ required: true, trigger: 'blur', message: '请输入您的账号' }],
          password: [{ required: true, trigger: 'blur', message: '请输入您的密码' }],
          code: [{ required: true, trigger: 'change', message: '请输入验证码' }]
        },
        loading: false,
        // 验证码开关
        captchaEnabled: false,
        // 注册开关
        register: false,
        redirect: undefined,
        themeClass: '', // 添加主题class
        isTokenLogin: false, // 是否为token登录模式
        tokenLoginFailed: false, // token登录是否失败
        currentToken: '' // 当前的token
      }
    },
    watch: {
      $route: {
        handler: function (route) {
          this.redirect = route.query && route.query.redirect
        },
        immediate: true
      }
    },
    created() {
      // 检查URL中是否有token参数
      const token = this.$route.query.token
      if (token) {
        // 有token，进入token登录模式
        this.isTokenLogin = true
        this.currentToken = token
        this.handleTokenLogin(token)
      } else {
        // 没有token，显示错误提示
        this.isTokenLogin = false
      }

      // 获取当前主题
      const theme = window.localStorage.getItem('theme') || 'theme-dark'
      this.themeClass = theme
    },
    methods: {
      getCode() {
        getCodeImg().then((res) => {
          this.captchaEnabled = res.captchaEnabled == null ? true : res.captchaEnabled
          if (this.captchaEnabled) {
            this.codeUrl = 'data:image/gif;base64,' + res.img
            this.loginForm.uuid = res.uuid
          }
        })
      },
      getCookie() {
        const username = Cookies.get('username')
        const password = Cookies.get('password')
        const rememberMe = Cookies.get('rememberMe')
        this.loginForm = {
          username: username === undefined ? this.loginForm.username : username,
          password: password === undefined ? this.loginForm.password : decrypt(password),
          rememberMe: rememberMe === undefined ? false : rememberMe === 'true'
        }
      },
      handleLogin() {
        this.$refs.loginForm.validate((valid) => {
          if (valid) {
            this.loading = true
            if (this.loginForm.rememberMe) {
              Cookies.set('username', this.loginForm.username, { expires: 30 })
              Cookies.set('password', encrypt(this.loginForm.password), { expires: 30 })
              Cookies.set('rememberMe', this.loginForm.rememberMe, { expires: 30 })
            } else {
              Cookies.remove('username')
              Cookies.remove('password')
              Cookies.remove('rememberMe')
            }
            this.$store
              .dispatch('Login', this.loginForm)
              .then(() => {
                this.$router.push({ path: this.redirect || '/performance' }) // 修改为新的成效看板路径
              })
              .catch(() => {
                this.loading = false
                if (this.captchaEnabled) {
                  this.getCode()
                }
              })
          }
        })
      },

      // Token登录处理
      handleTokenLogin(token) {
        this.loading = true
        this.tokenLoginFailed = false
        this.$store
          .dispatch('LoginWithToken', token)
          .then(() => {
            this.$router.push({ path: this.redirect || '/performance' })
          })
          .catch((error) => {
            this.loading = false
            this.tokenLoginFailed = true
            console.error('Token登录失败:', error)
          })
      },

      // 重试token登录
      retryTokenLogin() {
        this.handleTokenLogin(this.currentToken)
      }
    }
  }
</script>

<style rel="stylesheet/scss" lang="scss">
  .login {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100%;
    transition: background 0.3s ease;

    // 深色主题
    &.theme-dark {
      background: #013831;
      .title {
        color: #ffffff;
      }
      .login-form {
        background: rgba(255, 255, 255, 0.1);
        .el-input input {
          background: rgba(255, 255, 255, 0.1);
          border-color: rgba(255, 255, 255, 0.2);
          color: #ffffff;
          &::placeholder {
            color: rgba(255, 255, 255, 0.5);
          }
        }
        .el-checkbox__label {
          color: rgba(255, 255, 255, 0.8);
        }
        .link-type {
          color: rgba(255, 255, 255, 0.8);
        }
        .el-button--primary {
          background-color: #0e8e97;
          border-color: #0e8e97;
          &:hover,
          &:focus {
            background-color: #1a9ba4;
            border-color: #1a9ba4;
          }
        }
      }
    }

    // 浅色主题
    &.theme-light {
      background: #f0f2f5;
      .title {
        color: #1c663e;
      }
      .login-form {
        background: #ffffff;
        .el-button--primary {
          background-color: #1c663e;
          border-color: #1c663e;
          &:hover,
          &:focus {
            background-color: #2a744c;
            border-color: #2a744c;
          }
        }
      }
    }
  }

  .custom-loading-text {
    font-size: 14px;
    color: #ffffff;
  }

  .title {
    margin: 0px auto 30px auto;
    text-align: center;
    color: #1c663e;
    font-size: 24px;
    font-weight: bold;
  }

  .login-form {
    border-radius: 8px;
    background: #ffffff;
    width: 400px;
    padding: 35px 35px 15px 35px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);

    .el-input {
      height: 40px;

      input {
        height: 40px;
      }
    }

    .input-icon {
      height: 40px;
      width: 14px;
      margin-left: 2px;
    }

    .el-button--primary {
      background-color: #1c663e;
      border-color: #1c663e;

      &:hover,
      &:focus {
        background-color: #2a744c;
        border-color: #2a744c;
      }
    }
  }

  .login-tip {
    font-size: 13px;
    text-align: center;
    color: #bfbfbf;
  }

  .login-code {
    width: 33%;
    height: 38px;
    float: right;

    img {
      cursor: pointer;
      vertical-align: middle;
    }
  }

  .el-login-footer {
    height: 40px;
    line-height: 40px;
    position: fixed;
    bottom: 0;
    width: 100%;
    text-align: center;
    color: #fff;
    font-family: Arial;
    font-size: 12px;
    letter-spacing: 1px;
  }

  .login-code-img {
    height: 38px;
  }

  .token-login-container {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 200px;
  }

  .login-status {
    text-align: center;

    .el-icon-loading {
      animation: rotating 2s linear infinite;
    }

    p {
      margin-top: 16px;
      font-size: 16px;
      color: #606266;
    }
  }

  @keyframes rotating {
    0% {
      transform: rotate(0deg);
    }
    100% {
      transform: rotate(360deg);
    }
  }

  // 深色主题下的token登录状态
  .theme-dark .login-status p {
    color: rgba(255, 255, 255, 0.8);
  }
</style>
